# Users Service API Guide - Quản lý Người dùng

Hướng dẫn chi tiết cho Frontend Developer để xây dựng trang quản lý người dùng trong hệ thống.

## 📋 Tổng quan

Service Users cung cấp đầy đủ các API để quản lý người dùng với các chức năng:
- ✅ **Thêm người dùng** (Create)
- ✅ **Sửa thông tin người dùng** (Update) 
- ✅ **Xóa người dùng** (Delete - soft delete)
- ✅ **Tìm kiếm và lọc người dùng** (Search & Filter)
- ✅ **Phân trang** (Pagination)
- ✅ **Quản lý quyền và vai trò** (Roles & Permissions)

## 🔗 Base URL
```
/api/users
```

## 🔐 Authentication
Tất cả API đều yêu cầu authentication header:
```javascript
headers: {
  'Authorization': 'Bearer <access_token>'
}
```

## 📊 User Data Model

### User Object Structure:
```javascript
{
  "_id": "string",
  "email": "string",
  "fullName": "string", 
  "phone": "string",
  "gender": "string",
  "imageAvatarId": "ObjectId",
  "role": "admin|normal|contributor",
  "type": "student|teacher",
  "active": "boolean",
  "state": "active",
  "hasPassword": "boolean",
  "isDeveloper": "boolean",
  "lastLogin": "Date",
  "lastVisit": "Date",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

## 🚀 API Endpoints

### 1. **Lấy danh sách người dùng (với phân trang)**

```http
GET /api/users
```

**Query Parameters:**
```javascript
{
  "page": 1,           // Trang hiện tại (default: 1)
  "limit": 10,         // Số items per page (default: 10)
  "query": "{}",       // MongoDB query (JSON string)
  "sort": "-createdAt", // Sắp xếp (default: -createdAt)
  "searchFields": "fullName,email", // Tìm kiếm theo fields
  "search": "keyword"   // Từ khóa tìm kiếm
}
```

**Response:**
```javascript
{
  "docs": [
    {
      "_id": "user_id",
      "email": "<EMAIL>",
      "fullName": "Nguyễn Văn A",
      "phone": "0123456789",
      "role": "normal",
      "type": "student",
      "active": true,
      "state": "active",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalDocs": 100,
  "limit": 10,
  "page": 1,
  "totalPages": 10,
  "hasNextPage": true,
  "hasPrevPage": false,
  "nextPage": 2,
  "prevPage": null
}
```

### 2. **Lấy tất cả người dùng (không phân trang)**

```http
GET /api/users/findAll
```

**Query Parameters:**
```javascript
{
  "query": "{}",       // MongoDB query filter
  "searchFields": "fullName,email",
  "search": "keyword",
  "sort": "-createdAt"
}
```

### 3. **Lấy thông tin một người dùng**

```http
GET /api/users/:id
```

**Response:**
```javascript
{
  "_id": "user_id",
  "email": "<EMAIL>",
  "fullName": "Nguyễn Văn A",
  // ... other user fields
  
}
```

### 4. **Tạo người dùng mới**

```http
POST /api/users
```

**Request Body:**
```javascript
{
  "email": "<EMAIL>",     // Required
  "fullName": "Nguyễn Văn B",         // Required
  "phone": "0987654321",              // Optional
  "gender": "male",                   // Optional
  "role": "normal",                   // Optional (default: normal)
  "type": "student",                  // Optional
  "active": true,                     // Optional (default: false)
  "isSystemAdmin": false              // Optional (default: false)
}
```

**Response:**
```javascript
{
  "_id": "new_user_id",
  "email": "<EMAIL>",
  "fullName": "Nguyễn Văn B",
  "active": true,
  "state": "active",
  // ... other fields
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

### 5. **Cập nhật thông tin người dùng**

```http
PUT /api/users/:id
```

**Request Body:**
```javascript
{
  "fullName": "Nguyễn Văn C Updated",
  "phone": "0111222333",
  "gender": "female",
  "role": "admin",
  "type": "teacher",
  "active": true,
}
```

**Response:**
```javascript
{
  "_id": "user_id",
  "email": "<EMAIL>",
  "fullName": "Nguyễn Văn C Updated",
  // ... updated fields
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 6. **Xóa người dùng (Soft Delete)**

```http
DELETE /api/users/:id
```

**Response:**
```javascript
{
  "_id": "user_id",
  "isDeleted": true,
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```


## 🔒 Phân quyền và Bảo mật

### User Roles:
- **`admin`**: Quản trị viên tổ chức
- **`normal`**: Người dùng thông thường
- **`contributor`**: Người đóng góp nội dung

### User Types:
- **`student`**: Học viên
- **`teacher`**: Giảng viên

### User States:
- **`active`**: Tài khoản đã kích hoạt
- **`waitlist`**: Tài khoản trong danh sách chờ

### Permissions:
- **System Admin** (`isSystemAdmin: true`): Toàn quyền hệ thống
- **Organization Admin** (`role: "admin"`): Quản lý trong tổ chức
- **Normal User** (`role: "normal"`): Quyền cơ bản

## 📝 Validation Rules

### Email:
- ✅ Required
- ✅ Valid email format
- ✅ Unique trong hệ thống

### Full Name:
- ✅ Required
- ✅ Minimum 1 character

### Phone:
- ✅ Optional
- ✅ Unique nếu có

### Password:
- ✅ Minimum 6 characters (khi tạo mới)
- ✅ Tự động hash khi lưu

## 🎨 UI/UX Recommendations

### User List Table Columns:
1. **Avatar** - Ảnh đại diện
2. **Full Name** - Họ tên
3. **Email** - Email
4. **Phone** - Số điện thoại
5. **Role** - Vai trò (Badge)
6. **Type** - Loại người dùng (Badge)
7. **Status** - Trạng thái (Active/Inactive)
8. **Organization** - Tổ chức
9. **Last Login** - Lần đăng nhập cuối
10. **Actions** - Hành động (Edit/Delete)

### Filter Options:
- **Search Box**: Tìm theo tên, email
- **Role Filter**: Dropdown (All, Admin, Normal, Contributor)
- **Type Filter**: Dropdown (All, Student, Teacher)
- **Status Filter**: Dropdown (All, Active, Inactive)
- **Organization Filter**: Dropdown (nếu có nhiều tổ chức)

### Action Buttons:
- **➕ Add User**: Mở modal tạo user
- **✏️ Edit**: Mở modal chỉnh sửa
- **🗑️ Delete**: Xác nhận xóa
- **👁️ View**: Xem chi tiết

## 🚨 Important Notes

### 1. **Soft Delete:**
- Users không bị xóa vĩnh viễn
- Chỉ set `isDeleted: true`
- Không hiển thị trong list thông thường

### 2. **System Admin Protection:**
- Không thể xóa System Admin
- API sẽ trả về error nếu cố gắng xóa

### 3. **Email Uniqueness:**
- Email phải unique trong toàn hệ thống
- Check trước khi create/update

### 4. **Phone Uniqueness:**
- Phone number phải unique nếu có
- Check trước khi create/update

### 5. **Password Handling:**
- Password tự động hash
- Không trả về password trong response
- Field `hasPassword` cho biết user có password hay không

## 🔧 Testing

### Test Cases cần cover:

1. **CRUD Operations:**
   - ✅ Create user with valid data
   - ✅ Create user with duplicate email (should fail)
   - ✅ Update user information
   - ✅ Delete user (soft delete)
   - ✅ Get user list with pagination

2. **Search & Filter:**
   - ✅ Search by name/email
   - ✅ Filter by role
   - ✅ Filter by status
   - ✅ Combined filters

3. **Validation:**
   - ✅ Required fields validation
   - ✅ Email format validation
   - ✅ Unique email validation
   - ✅ Unique phone validation

4. **Permissions:**
   - ✅ System admin can manage all users
   - ✅ Org admin can manage org users only
   - ✅ Normal user cannot access admin APIs

## 📞 Support

Nếu có vấn đề hoặc cần hỗ trợ thêm về API, vui lòng liên hệ Backend Team hoặc tạo issue trong project repository.

---

**Last Updated:** 2024-01-15
**API Version:** v1.0
**Author:** Backend Development Team
```
